{"boosting_type": "gbdt", "class_weight": "balanced", "colsample_bytree": 0.7, "importance_type": "split", "learning_rate": 0.01, "max_depth": 3, "min_child_samples": 50, "min_child_weight": 0.001, "min_split_gain": 0.0, "n_estimators": 2000, "n_jobs": -1, "num_leaves": 8, "objective": "multiclass", "random_state": 2024, "reg_alpha": 5.0, "reg_lambda": 5.0, "subsample": 0.7, "subsample_for_bin": 200000, "subsample_freq": 0, "num_class": 3, "metric": "multi_logloss", "device_type": "cpu", "verbosity": 0}