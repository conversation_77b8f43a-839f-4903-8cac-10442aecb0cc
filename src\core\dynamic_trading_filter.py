#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
动态交易过滤器 - 基于市场状态的硬性规则过滤器
在最终发出交易信号前，基于市场状态进行智能过滤
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Any, Union
from enum import Enum
import logging

from .market_state_analyzer import MarketStateAnalyzer, MarketRegime

logger = logging.getLogger(__name__)


class FilterAction(Enum):
    """过滤器动作枚举"""
    ALLOW = "allow"                    # 允许信号通过
    BLOCK = "block"                    # 阻止信号
    REDUCE_CONFIDENCE = "reduce"       # 降低信号置信度
    NEUTRAL_OVERRIDE = "neutral"       # 强制转为中性


class DynamicTradingFilter:
    """动态交易过滤器"""
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        初始化动态交易过滤器
        
        Args:
            config: 过滤器配置
        """
        self.config = config or self._get_default_config()
        
        # 初始化市场状态分析器
        self.market_analyzer = MarketStateAnalyzer(self.config.get('market_analyzer_config', {}))
        
        # 过滤历史
        self.filter_history: List[Dict] = []
        
        # 统计信息
        self.filter_stats = {
            'total_signals': 0,
            'blocked_signals': 0,
            'reduced_signals': 0,
            'allowed_signals': 0,
            'neutral_overrides': 0
        }
        
        logger.info("[DynamicTradingFilter] 动态交易过滤器初始化完成")
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            # 过滤规则配置
            'enable_market_state_filter': True,        # 是否启用市场状态过滤
            'enable_volatility_filter': True,          # 是否启用波动率过滤
            'enable_trend_consistency_filter': True,   # 是否启用趋势一致性过滤
            'enable_confidence_adjustment': True,      # 是否启用置信度调整
            
            # 阻止信号的条件
            'block_on_high_vol_sideways': True,        # 高波动盘整时阻止信号
            'block_danger_score_threshold': 0.8,       # 危险评分阈值，超过则阻止
            'block_on_extreme_volatility': True,       # 极端波动时阻止
            'extreme_volatility_atr_threshold': 4.0,   # 极端波动ATR阈值
            
            # 降低置信度的条件
            'reduce_confidence_danger_threshold': 0.5, # 危险评分阈值，超过则降低置信度
            'confidence_reduction_factor': 0.7,        # 置信度降低因子
            'reduce_on_moderate_volatility': True,     # 中等波动时降低置信度
            
            # 趋势一致性检查
            'require_trend_signal_alignment': True,    # 要求信号与趋势方向一致
            'trend_alignment_adx_threshold': 25,       # 趋势一致性检查的ADX阈值
            
            # 特殊市场状态处理
            'neutral_override_regimes': [              # 强制转为中性的市场状态
                MarketRegime.HIGH_VOLATILITY_SIDEWAYS
            ],
            
            # 市场状态分析器配置
            'market_analyzer_config': {
                'high_volatility_atr_threshold': 2.5,
                'low_trend_strength_adx_threshold': 20,
                'strong_trend_adx_threshold': 30
            }
        }
    
    def apply_filter(self, signal: str, signal_probabilities: List[float], 
                    global_market_data: Dict[str, float],
                    recent_price_data: pd.DataFrame = None,
                    meta_model_probabilities: List[float] = None) -> Dict[str, Any]:
        """
        应用动态交易过滤器
        
        Args:
            signal: 原始信号 ("UP", "DOWN", "Neutral")
            signal_probabilities: 信号概率 [p_down, p_up, p_neutral]
            global_market_data: 全局市场状态数据
            recent_price_data: 最近的价格数据（可选）
            meta_model_probabilities: 元模型概率（可选）
            
        Returns:
            过滤结果字典
        """
        filter_result = {
            'timestamp': pd.Timestamp.now().isoformat(),
            'original_signal': signal,
            'original_probabilities': signal_probabilities.copy() if signal_probabilities else [0.33, 0.33, 0.33],
            'filtered_signal': signal,
            'filtered_probabilities': signal_probabilities.copy() if signal_probabilities else [0.33, 0.33, 0.33],
            'filter_action': FilterAction.ALLOW,
            'filter_reasons': [],
            'market_analysis': {},
            'confidence_adjustment': 1.0,
            'should_trade': True
        }
        
        try:
            # 更新统计
            self.filter_stats['total_signals'] += 1
            
            # 1. 市场状态分析
            if self.config['enable_market_state_filter']:
                market_analysis = self.market_analyzer.analyze_market_state(
                    global_market_data, recent_price_data
                )
                filter_result['market_analysis'] = market_analysis
                
                # 2. 应用市场状态过滤规则
                market_filter_result = self._apply_market_state_rules(
                    signal, signal_probabilities, market_analysis
                )
                
                # 更新过滤结果
                filter_result.update(market_filter_result)
            
            # 3. 应用波动率过滤
            if self.config['enable_volatility_filter']:
                volatility_filter_result = self._apply_volatility_filter(
                    filter_result['filtered_signal'], 
                    filter_result['filtered_probabilities'],
                    global_market_data
                )
                
                # 合并过滤结果
                if volatility_filter_result['filter_action'] != FilterAction.ALLOW:
                    filter_result.update(volatility_filter_result)
            
            # 4. 应用趋势一致性过滤
            if self.config['enable_trend_consistency_filter']:
                trend_filter_result = self._apply_trend_consistency_filter(
                    filter_result['filtered_signal'],
                    global_market_data
                )
                
                # 合并过滤结果
                if trend_filter_result['filter_action'] != FilterAction.ALLOW:
                    filter_result['filter_reasons'].extend(trend_filter_result['filter_reasons'])
                    if trend_filter_result['filter_action'] == FilterAction.BLOCK:
                        filter_result['filter_action'] = FilterAction.BLOCK
                        filter_result['filtered_signal'] = "Neutral_TrendFiltered"
                        filter_result['should_trade'] = False
            
            # 5. 最终决策
            self._finalize_filter_decision(filter_result)
            
            # 记录过滤历史
            self.filter_history.append(filter_result)
            
            # 输出过滤结果
            if filter_result['filter_action'] != FilterAction.ALLOW:
                logger.info(f"[DynamicTradingFilter] 信号过滤: {signal} -> {filter_result['filtered_signal']}")
                logger.info(f"[DynamicTradingFilter] 过滤原因: {', '.join(filter_result['filter_reasons'])}")
            
            return filter_result
            
        except Exception as e:
            logger.error(f"[DynamicTradingFilter] 过滤器应用失败: {e}")
            filter_result['error'] = str(e)
            return filter_result
    
    def _apply_market_state_rules(self, signal: str, probabilities: List[float], 
                                market_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """应用市场状态过滤规则"""
        result = {
            'filtered_signal': signal,
            'filtered_probabilities': probabilities.copy(),
            'filter_action': FilterAction.ALLOW,
            'filter_reasons': [],
            'confidence_adjustment': 1.0,
            'should_trade': True
        }
        
        market_regime = market_analysis.get('market_regime', MarketRegime.UNKNOWN)
        danger_score = market_analysis.get('danger_score', 0.0)
        trading_recommendation = market_analysis.get('trading_recommendation', 'neutral')
        
        # 规则1: 高波动盘整期 - 强制阻止
        if (self.config['block_on_high_vol_sideways'] and 
            market_regime == MarketRegime.HIGH_VOLATILITY_SIDEWAYS):
            result['filter_action'] = FilterAction.BLOCK
            result['filtered_signal'] = "Neutral_HighVolSideways"
            result['should_trade'] = False
            result['filter_reasons'].append("高波动盘整期，阻止交易")
            self.filter_stats['blocked_signals'] += 1
            return result
        
        # 规则2: 高危险评分 - 阻止
        if danger_score > self.config['block_danger_score_threshold']:
            result['filter_action'] = FilterAction.BLOCK
            result['filtered_signal'] = "Neutral_HighDanger"
            result['should_trade'] = False
            result['filter_reasons'].append(f"高危险评分({danger_score:.2f})")
            self.filter_stats['blocked_signals'] += 1
            return result
        
        # 规则3: 强制中性状态
        if market_regime in self.config['neutral_override_regimes']:
            result['filter_action'] = FilterAction.NEUTRAL_OVERRIDE
            result['filtered_signal'] = "Neutral_MarketRegime"
            result['should_trade'] = False
            result['filter_reasons'].append(f"市场状态强制中性({market_regime.value})")
            self.filter_stats['neutral_overrides'] += 1
            return result
        
        # 规则4: 中等危险 - 降低置信度
        if (danger_score > self.config['reduce_confidence_danger_threshold'] and
            self.config['enable_confidence_adjustment']):
            confidence_factor = self.config['confidence_reduction_factor']
            result['confidence_adjustment'] = confidence_factor
            result['filter_action'] = FilterAction.REDUCE_CONFIDENCE
            result['filter_reasons'].append(f"中等风险，置信度降低至{confidence_factor:.1%}")
            
            # 调整概率
            if signal in ["UP", "DOWN"]:
                signal_idx = 1 if signal == "UP" else 0
                neutral_idx = 2
                
                # 降低信号概率，增加中性概率
                original_signal_prob = probabilities[signal_idx]
                reduction = original_signal_prob * (1 - confidence_factor)
                
                result['filtered_probabilities'][signal_idx] = original_signal_prob - reduction
                result['filtered_probabilities'][neutral_idx] += reduction
            
            self.filter_stats['reduced_signals'] += 1
        
        return result
    
    def _apply_volatility_filter(self, signal: str, probabilities: List[float],
                               global_market_data: Dict[str, float]) -> Dict[str, Any]:
        """应用波动率过滤"""
        result = {
            'filter_action': FilterAction.ALLOW,
            'filter_reasons': []
        }
        
        atr_percent = global_market_data.get('global_atr_percent', 0.0)
        
        # 极端波动率 - 阻止交易
        if (self.config['block_on_extreme_volatility'] and 
            atr_percent > self.config['extreme_volatility_atr_threshold']):
            result['filter_action'] = FilterAction.BLOCK
            result['filtered_signal'] = "Neutral_ExtremeVolatility"
            result['should_trade'] = False
            result['filter_reasons'].append(f"极端波动率({atr_percent:.2f}%)")
            self.filter_stats['blocked_signals'] += 1
        
        return result
    
    def _apply_trend_consistency_filter(self, signal: str, 
                                      global_market_data: Dict[str, float]) -> Dict[str, Any]:
        """应用趋势一致性过滤"""
        result = {
            'filter_action': FilterAction.ALLOW,
            'filter_reasons': []
        }
        
        if not self.config['require_trend_signal_alignment']:
            return result
        
        adx_value = global_market_data.get('global_adx', 0.0)
        ema_diff_pct = global_market_data.get('global_ema_diff_pct', 0.0)
        
        # 只在有明确趋势时检查一致性
        if adx_value > self.config['trend_alignment_adx_threshold']:
            trend_direction = "UP" if ema_diff_pct > 0 else "DOWN"
            
            # 检查信号与趋势方向是否一致
            if signal in ["UP", "DOWN"] and signal != trend_direction:
                result['filter_action'] = FilterAction.BLOCK
                result['filter_reasons'].append(f"信号({signal})与趋势({trend_direction})不一致")
        
        return result
    
    def _finalize_filter_decision(self, filter_result: Dict[str, Any]):
        """最终化过滤决策"""
        # 更新统计
        if filter_result['filter_action'] == FilterAction.ALLOW:
            self.filter_stats['allowed_signals'] += 1
        
        # 确保概率和为1
        probabilities = filter_result['filtered_probabilities']
        prob_sum = sum(probabilities)
        if prob_sum > 0:
            filter_result['filtered_probabilities'] = [p / prob_sum for p in probabilities]
    
    def get_filter_statistics(self) -> Dict[str, Any]:
        """获取过滤器统计信息"""
        total = self.filter_stats['total_signals']
        if total == 0:
            return {'status': 'no_data'}
        
        return {
            'total_signals': total,
            'allowed_rate': self.filter_stats['allowed_signals'] / total,
            'blocked_rate': self.filter_stats['blocked_signals'] / total,
            'reduced_rate': self.filter_stats['reduced_signals'] / total,
            'neutral_override_rate': self.filter_stats['neutral_overrides'] / total,
            'recent_filter_actions': [h['filter_action'].value for h in self.filter_history[-10:]],
            'recent_market_regimes': [h.get('market_analysis', {}).get('market_regime', MarketRegime.UNKNOWN).value
                                    for h in self.filter_history[-10:] if 'market_analysis' in h]
        }
    
    def should_allow_signal(self, signal: str, global_market_data: Dict[str, float]) -> Tuple[bool, List[str]]:
        """
        快速检查是否应该允许信号（简化版本）
        
        Args:
            signal: 信号类型
            global_market_data: 全局市场数据
            
        Returns:
            (是否允许, 阻止原因列表)
        """
        reasons = []
        
        # 快速市场状态检查
        atr_percent = global_market_data.get('global_atr_percent', 0.0)
        adx_value = global_market_data.get('global_adx', 0.0)
        
        # 高波动 + 低趋势强度 = 危险
        if (atr_percent > self.config['market_analyzer_config']['high_volatility_atr_threshold'] and
            adx_value < self.config['market_analyzer_config']['low_trend_strength_adx_threshold']):
            reasons.append("高波动盘整期")
            return False, reasons
        
        # 极端波动率
        if atr_percent > self.config['extreme_volatility_atr_threshold']:
            reasons.append(f"极端波动率({atr_percent:.2f}%)")
            return False, reasons
        
        return True, reasons
