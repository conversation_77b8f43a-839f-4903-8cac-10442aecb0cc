{"target_name": "BTC_15m_DOWN", "timestamp_utc": "2025-06-14T11:45:42.078143+00:00", "scaler_filename": "scaler_BTC_15m_DOWN_30m.joblib", "feature_list_filename": "final_selected_features_impthresh_BTC_15m_DOWN_30m.json", "fold_model_artifacts": [{"model_filename": "model_BTC_15m_DOWN_30m_fold0_impthresh_optuna.joblib", "shap_explainer_filename": "shap_explainer_BTC_15m_DOWN_30m_fold0_impthresh_optuna.joblib", "calibrated_model_filename": "model_BTC_15m_DOWN_30m_fold0_impthresh_optuna_calibrated.joblib"}, {"model_filename": "model_BTC_15m_DOWN_30m_fold1_impthresh_optuna.joblib", "calibrated_model_filename": "model_BTC_15m_DOWN_30m_fold1_impthresh_optuna_calibrated.joblib"}, {"model_filename": "model_BTC_15m_DOWN_30m_fold2_impthresh_optuna.joblib", "calibrated_model_filename": "model_BTC_15m_DOWN_30m_fold2_impthresh_optuna_calibrated.joblib"}, {"model_filename": "model_BTC_15m_DOWN_30m_fold3_impthresh_optuna.joblib", "calibrated_model_filename": "model_BTC_15m_DOWN_30m_fold3_impthresh_optuna_calibrated.joblib"}, {"model_filename": "model_BTC_15m_DOWN_30m_fold4_impthresh_optuna.joblib", "calibrated_model_filename": "model_BTC_15m_DOWN_30m_fold4_impthresh_optuna_calibrated.joblib"}], "final_training_summary_metrics_for_gui": {"status": "元数据保存时状态未知"}, "detailed_fold_evaluations": [{"fold_index": 0, "model_type": "LGBM_Fold", "best_iteration_raw": 1, "raw_train_accuracy": 0.7524103355187042, "raw_train_brier": 0.24837388916213873, "raw_internal_val_brier": 0.25021732049398004, "calibrated_internal_val_brier": 0.18363096349222996, "calibration_selected": true, "optimal_threshold": 0.21000000000000002, "threshold_f1_score": 0.42610364683301344, "threshold_precision": 0.2833078101071976, "threshold_recall": 0.8591331269349846, "threshold_accuracy": 0.42322530864197533, "threshold_method": "f1", "test_model_type": "Calibrated", "test_accuracy": 0.6446578631452581, "test_brier": 0.14333932009463937, "test_classification_report": {"下跌 (0)": {"precision": 0.910917979359044, "recall": 0.6218020022246941, "f1-score": 0.7390921110621419, "support": 2697.0}, "上涨 (1)": {"precision": 0.3158953722334004, "recall": 0.7417322834645669, "f1-score": 0.4430856067732832, "support": 635.0}, "accuracy": 0.6446578631452581, "macro avg": {"precision": 0.6134066757962222, "recall": 0.6817671428446305, "f1-score": 0.5910888589177126, "support": 3332.0}, "weighted avg": {"precision": 0.7975208138353994, "recall": 0.644657863145258, "f1-score": 0.6826803072735989, "support": 3332.0}}, "test_optimal_threshold_used": 0.21000000000000002, "ensemble_optimal_threshold": 0.1, "ensemble_threshold_f1_score": 0.3897053843548933, "ensemble_threshold_precision": 0.24855291576673866, "ensemble_threshold_recall": 0.9018808777429467, "ensemble_threshold_accuracy": 0.3047067901234568, "ensemble_threshold_method": "f1"}, {"fold_index": 1, "model_type": "LGBM_Fold", "best_iteration_raw": 2063, "raw_train_accuracy": 0.9353905496624879, "raw_train_brier": 0.0667105626956701, "raw_internal_val_brier": 0.1725885645701042, "calibrated_internal_val_brier": 0.1621913097598484, "calibration_selected": true, "optimal_threshold": 0.71, "threshold_f1_score": 0.006420545746388443, "threshold_precision": 0.6666666666666666, "threshold_recall": 0.0032258064516129032, "threshold_accuracy": 0.7611882716049383, "threshold_method": "f1", "test_model_type": "Calibrated", "test_accuracy": 0.8181272509003601, "test_brier": 0.12500300070924342, "test_classification_report": {"下跌 (0)": {"precision": 0.8213956347986474, "recall": 0.9907304412309974, "f1-score": 0.8981512605042017, "support": 2697.0}, "上涨 (1)": {"precision": 0.6835443037974683, "recall": 0.08503937007874016, "f1-score": 0.15126050420168066, "support": 635.0}, "accuracy": 0.8181272509003601, "macro avg": {"precision": 0.7524699692980579, "recall": 0.5378849056548688, "f1-score": 0.5247058823529411, "support": 3332.0}, "weighted avg": {"precision": 0.7951244477681106, "recall": 0.8181272509003601, "f1-score": 0.7558116355786012, "support": 3332.0}}, "test_optimal_threshold_used": 0.71}, {"fold_index": 2, "model_type": "LGBM_Fold", "best_iteration_raw": 2819, "raw_train_accuracy": 0.9351935193519352, "raw_train_brier": 0.0663522451091138, "raw_internal_val_brier": 0.18008466231334969, "calibrated_internal_val_brier": 0.1721035781596689, "calibration_selected": true, "optimal_threshold": 0.59, "threshold_f1_score": 0.2198581560283688, "threshold_precision": 0.62, "threshold_recall": 0.1336206896551724, "threshold_accuracy": 0.7453703703703703, "threshold_method": "f1", "test_model_type": "Calibrated", "test_accuracy": 0.8331332533013205, "test_brier": 0.12345109809150377, "test_classification_report": {"下跌 (0)": {"precision": 0.852022361065439, "recall": 0.960697070819429, "f1-score": 0.9031021261763681, "support": 2697.0}, "上涨 (1)": {"precision": 0.6357388316151202, "recall": 0.29133858267716534, "f1-score": 0.39956803455723544, "support": 635.0}, "accuracy": 0.8331332533013205, "macro avg": {"precision": 0.7438805963402797, "recall": 0.6260178267482972, "f1-score": 0.6513350803668018, "support": 3332.0}, "weighted avg": {"precision": 0.8108038613052493, "recall": 0.8331332533013205, "f1-score": 0.8071404970712812, "support": 3332.0}}, "test_optimal_threshold_used": 0.59}, {"fold_index": 3, "model_type": "LGBM_Fold", "best_iteration_raw": 2160, "raw_train_accuracy": 0.8885138393287685, "raw_train_brier": 0.09514085114922147, "raw_internal_val_brier": 0.16195160232070038, "calibrated_internal_val_brier": 0.15536967818224998, "calibration_selected": true, "optimal_threshold": 0.53, "threshold_f1_score": 0.35777777777777775, "threshold_precision": 0.6216216216216216, "threshold_recall": 0.25117004680187205, "threshold_accuracy": 0.7770061728395061, "threshold_method": "f1", "test_model_type": "Calibrated", "test_accuracy": 0.8319327731092437, "test_brier": 0.12220232526658761, "test_classification_report": {"下跌 (0)": {"precision": 0.8685753708175232, "recall": 0.9336299592139414, "f1-score": 0.899928520371694, "support": 2697.0}, "上涨 (1)": {"precision": 0.5866050808314087, "recall": 0.4, "f1-score": 0.4756554307116105, "support": 635.0}, "accuracy": 0.8319327731092437, "macro avg": {"precision": 0.727590225824466, "recall": 0.6668149796069707, "f1-score": 0.6877919755416523, "support": 3332.0}, "weighted avg": {"precision": 0.8148385358411779, "recall": 0.8319327731092437, "f1-score": 0.8190721542449975, "support": 3332.0}}, "test_optimal_threshold_used": 0.53}, {"fold_index": 4, "model_type": "LGBM_Fold", "best_iteration_raw": 28, "raw_train_accuracy": 0.6994830645783504, "raw_train_brier": 0.22695591460214495, "raw_internal_val_brier": 0.24678130156833422, "calibrated_internal_val_brier": 0.17466853760868012, "calibration_selected": true, "optimal_threshold": 0.2, "threshold_f1_score": 0.3741200134093195, "threshold_precision": 0.2328881469115192, "threshold_recall": 0.9505962521294719, "threshold_accuracy": 0.2797067901234568, "threshold_method": "f1", "test_model_type": "Calibrated", "test_accuracy": 0.6434573829531812, "test_brier": 0.1403248107367535, "test_classification_report": {"下跌 (0)": {"precision": 0.9333716255025847, "recall": 0.6025213199851687, "f1-score": 0.7323118521856692, "support": 2697.0}, "上涨 (1)": {"precision": 0.3262099308610937, "recall": 0.8173228346456692, "f1-score": 0.46630727762803237, "support": 635.0}, "accuracy": 0.6434573829531812, "macro avg": {"precision": 0.6297907781818392, "recall": 0.7099220773154189, "f1-score": 0.5993095649068507, "support": 3332.0}, "weighted avg": {"precision": 0.817661038438555, "recall": 0.6434573829531812, "f1-score": 0.6816177030727943, "support": 3332.0}}, "test_optimal_threshold_used": 0.2}], "rfe_succeeded": false, "initial_importance_succeeded": true, "optuna_completed_successfully": true, "rfe_details": {"enabled": false, "original_features_count": 570, "selected_features_count": null, "features_removed_count": null, "selected_features": null, "rfe_config": null}, "optuna_details": {"enabled": true, "n_trials": 150, "best_params": {"learning_rate": 0.013836480127054988, "num_leaves": 22, "max_depth": 5, "reg_alpha": 7.170810970884177, "reg_lambda": 4.800195950174082, "colsample_bytree": 0.5421690586774313, "subsample": 0.5347079438501737, "min_child_samples": 74}, "optuna_config": {"metric": "average_precision", "direction": "maximize", "cv_folds": 3, "timeout": 7200}}, "model_suffix_parts_used_for_artifacts": "_impthresh_optuna", "ensemble_cv_folds_used": 5}