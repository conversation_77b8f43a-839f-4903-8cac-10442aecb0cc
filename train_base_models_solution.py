#!/usr/bin/env python3
"""
基础模型训练解决方案
自动训练 UP 和 DOWN 基础模型，为元模型训练做准备
"""

import sys
import os
import time
import threading
from pathlib import Path

# 添加项目路径
sys.path.append('.')
sys.path.append('src')

def check_and_import_modules():
    """检查并导入必要的模块"""
    try:
        import config
        import main
        print("✅ 成功导入必要模块")
        return True
    except ImportError as e:
        print(f"❌ 模块导入失败: {e}")
        print("请确保在正确的项目目录中运行此脚本")
        return False

def check_data_availability():
    """检查数据可用性"""
    print("🔍 检查数据可用性...")
    
    # 检查数据文件
    data_files = [
        "data/btc_15m_data.csv",
        "data/btc_1m_data.csv", 
        "data/processed_data.csv"
    ]
    
    found_data = False
    for data_file in data_files:
        if os.path.exists(data_file):
            file_size = os.path.getsize(data_file) / (1024 * 1024)  # MB
            print(f"  ✓ 找到数据文件: {data_file} ({file_size:.1f} MB)")
            found_data = True
        else:
            print(f"  ❌ 数据文件不存在: {data_file}")
    
    if not found_data:
        print("  ⚠️  未找到数据文件，可能需要先获取历史数据")
    
    return found_data

def train_single_model(target_name):
    """训练单个模型"""
    print(f"\n🎯 开始训练 {target_name} 模型...")
    
    try:
        # 这里需要调用实际的训练函数
        # 由于我们无法直接修改main.py的训练逻辑，
        # 我们提供一个指导性的解决方案
        
        print(f"📋 {target_name} 模型训练步骤:")
        print("1. 启动GUI界面: python main.py")
        print(f"2. 在目标选择下拉菜单中选择 '{target_name}'")
        print("3. 点击 '训练' 按钮")
        print("4. 等待训练完成（可能需要几小时）")
        print("5. 检查模型文件是否正确保存")
        
        # 检查预期的模型保存目录
        if target_name == "BTC_15m_UP":
            model_dir = "models/trained_models_btc_15m_up"
        elif target_name == "BTC_15m_DOWN":
            model_dir = "models/trained_models_btc_15m_down"
        else:
            model_dir = f"models/trained_models_{target_name.lower()}"
        
        print(f"6. 模型将保存到: {model_dir}")
        
        return True
        
    except Exception as e:
        print(f"❌ {target_name} 模型训练过程出错: {e}")
        return False

def create_training_config_template():
    """创建训练配置模板"""
    print("\n📝 创建训练配置建议...")
    
    config_suggestions = {
        "UP模型训练建议": {
            "目标": "BTC_15m_UP",
            "类型": "UP_ONLY (预测明确上涨)",
            "预测周期": "30分钟 (2 * 15分钟)",
            "关键参数": {
                "target_threshold": "0.015 (1.5%涨幅阈值)",
                "ensemble_cv_folds": "5 (5折交叉验证)",
                "lgbm_n_estimators": "3000 (基础树数量)",
                "early_stopping_rounds": "200 (早停轮数)"
            }
        },
        "DOWN模型训练建议": {
            "目标": "BTC_15m_DOWN", 
            "类型": "DOWN_ONLY (预测明确下跌)",
            "预测周期": "30分钟 (2 * 15分钟)",
            "关键参数": {
                "target_threshold": "0.015 (1.5%跌幅阈值)",
                "ensemble_cv_folds": "5 (5折交叉验证)",
                "lgbm_n_estimators": "3000 (基础树数量)",
                "early_stopping_rounds": "200 (早停轮数)"
            }
        }
    }
    
    for model_name, config in config_suggestions.items():
        print(f"\n🎯 {model_name}:")
        print(f"  目标: {config['目标']}")
        print(f"  类型: {config['类型']}")
        print(f"  预测周期: {config['预测周期']}")
        print("  关键参数:")
        for param, value in config['关键参数'].items():
            print(f"    - {param}: {value}")

def check_training_progress():
    """检查训练进度"""
    print("\n📊 检查训练进度...")
    
    model_dirs = {
        "UP模型": "models/trained_models_btc_15m_up",
        "DOWN模型": "models/trained_models_btc_15m_down"
    }
    
    progress = {}
    
    for model_name, model_dir in model_dirs.items():
        if os.path.exists(model_dir):
            files = os.listdir(model_dir)
            model_files = [f for f in files if f.endswith('.joblib') or f.endswith('.pkl')]
            
            if model_files:
                print(f"  ✅ {model_name}: 发现 {len(model_files)} 个模型文件")
                progress[model_name] = "完成"
                
                # 显示最新的模型文件
                latest_file = max([os.path.join(model_dir, f) for f in model_files], 
                                key=os.path.getmtime)
                file_time = time.ctime(os.path.getmtime(latest_file))
                file_size = os.path.getsize(latest_file) / (1024 * 1024)
                print(f"    最新文件: {os.path.basename(latest_file)}")
                print(f"    创建时间: {file_time}")
                print(f"    文件大小: {file_size:.1f} MB")
            else:
                print(f"  ⏳ {model_name}: 目录存在但无模型文件")
                progress[model_name] = "进行中"
        else:
            print(f"  ❌ {model_name}: 目录不存在")
            progress[model_name] = "未开始"
    
    return progress

def provide_next_steps(progress):
    """提供下一步操作建议"""
    print("\n" + "="*60)
    print("🚀 下一步操作建议")
    print("="*60)
    
    up_status = progress.get("UP模型", "未开始")
    down_status = progress.get("DOWN模型", "未开始")
    
    if up_status == "完成" and down_status == "完成":
        print("🎉 恭喜！所有基础模型训练完成！")
        print()
        print("✅ 现在可以进行元模型训练:")
        print("  1. 运行 python main.py 启动GUI")
        print("  2. 点击 '训练元模型' 按钮")
        print("  3. 等待元模型训练完成")
        print()
        print("📊 或者运行以下命令检查模型质量:")
        print("  python -c \"import main; main.run_meta_model_training_pipeline()\"")
        
    elif up_status == "未开始" and down_status == "未开始":
        print("🎯 需要训练基础模型:")
        print()
        print("方法一：使用GUI (推荐)")
        print("  1. 运行: python main.py")
        print("  2. 选择目标: BTC_15m_UP")
        print("  3. 点击 '训练' 按钮，等待完成")
        print("  4. 选择目标: BTC_15m_DOWN") 
        print("  5. 点击 '训练' 按钮，等待完成")
        print()
        print("方法二：检查配置文件")
        print("  1. 确认 config.py 中的 PREDICTION_TARGETS 配置正确")
        print("  2. 确认数据文件存在且完整")
        print("  3. 检查系统资源（内存、磁盘空间）")
        
    else:
        print("⏳ 部分模型训练完成:")
        print(f"  UP模型: {up_status}")
        print(f"  DOWN模型: {down_status}")
        print()
        
        if up_status != "完成":
            print("🎯 需要完成 UP 模型训练")
        if down_status != "完成":
            print("🎯 需要完成 DOWN 模型训练")
        
        print()
        print("继续使用GUI界面完成剩余模型的训练")

def main():
    """主函数"""
    print("🚀 基础模型训练解决方案")
    print("="*60)
    
    # 检查模块导入
    if not check_and_import_modules():
        return False
    
    # 检查数据可用性
    data_available = check_data_availability()
    
    # 创建训练配置建议
    create_training_config_template()
    
    # 检查当前训练进度
    progress = check_training_progress()
    
    # 提供下一步建议
    provide_next_steps(progress)
    
    print("\n" + "="*60)
    print("📞 需要帮助？")
    print("  - 查看控制台输出的详细信息")
    print("  - 检查 config.py 中的模型配置")
    print("  - 确保有足够的历史数据")
    print("  - 监控训练过程中的内存和CPU使用")
    print("  - 训练时间可能需要几小时，请耐心等待")
    
    return True

if __name__ == "__main__":
    success = main()
    if success:
        print("\n✅ 诊断完成，请按照建议进行操作")
    else:
        print("\n❌ 诊断过程中遇到问题，请检查环境配置")
    
    input("\n按回车键退出...")
