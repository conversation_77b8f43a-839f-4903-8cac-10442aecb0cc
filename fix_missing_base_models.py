#!/usr/bin/env python3
"""
修复缺失基础模型问题的解决方案
检查基础模型状态并提供训练建议
"""

import os
import sys
import json
from pathlib import Path

def check_model_directories():
    """检查模型目录结构"""
    print("🔍 检查模型目录结构...")
    
    model_dirs = {
        "UP模型目录": "models/trained_models_btc_15m_up",
        "DOWN模型目录": "models/trained_models_btc_15m_down", 
        "元模型目录": "models/meta_model_data"
    }
    
    for name, path in model_dirs.items():
        if os.path.exists(path):
            files = os.listdir(path)
            print(f"  ✓ {name}: {path}")
            if files:
                print(f"    包含 {len(files)} 个文件:")
                for file in files[:5]:  # 只显示前5个文件
                    print(f"      - {file}")
                if len(files) > 5:
                    print(f"      ... 还有 {len(files) - 5} 个文件")
            else:
                print(f"    ⚠️  目录为空")
        else:
            print(f"  ❌ {name}: {path} (不存在)")
            # 创建目录
            os.makedirs(path, exist_ok=True)
            print(f"    ✓ 已创建目录: {path}")

def check_expected_model_files():
    """检查预期的模型文件"""
    print("\n🎯 检查预期的基础模型文件...")
    
    # 基于错误信息中的文件名列表
    expected_files = {
        "UP模型": {
            "dir": "models/trained_models_btc_15m_up",
            "files": [
                "model_BTC_15m_UP_30m.pkl",
                "model_BTC_15m_UP_30m_fold4_rfe_calibrated.joblib",
                "model_BTC_15m_UP_30m_fold4_rfe.joblib",
                "model_BTC_15m_UP_30m_fold0_optuna_rfe_calibrated.joblib",
                "model_BTC_15m_UP_30m_fold0_optuna_rfe.joblib"
            ]
        },
        "DOWN模型": {
            "dir": "models/trained_models_btc_15m_down", 
            "files": [
                "model_BTC_15m_DOWN_30m.pkl",
                "model_BTC_15m_DOWN_30m_fold4_rfe_calibrated.joblib",
                "model_BTC_15m_DOWN_30m_fold4_rfe.joblib",
                "model_BTC_15m_DOWN_30m_fold0_optuna_rfe_calibrated.joblib",
                "model_BTC_15m_DOWN_30m_fold0_optuna_rfe.joblib"
            ]
        }
    }
    
    missing_files = []
    
    for model_type, info in expected_files.items():
        print(f"\n  📁 {model_type} ({info['dir']}):")
        found_any = False
        
        for filename in info['files']:
            filepath = os.path.join(info['dir'], filename)
            if os.path.exists(filepath):
                file_size = os.path.getsize(filepath) / (1024 * 1024)  # MB
                print(f"    ✓ {filename} ({file_size:.1f} MB)")
                found_any = True
            else:
                print(f"    ❌ {filename}")
        
        if not found_any:
            missing_files.append(model_type)
    
    return missing_files

def check_config_files():
    """检查配置文件"""
    print("\n⚙️  检查配置文件...")
    
    config_files = [
        "config.py",
        "backtest_config.py", 
        "dynamic_params.json"
    ]
    
    for config_file in config_files:
        if os.path.exists(config_file):
            print(f"  ✓ {config_file}")
        else:
            print(f"  ❌ {config_file} (缺失)")

def generate_training_script():
    """生成基础模型训练脚本"""
    print("\n📝 生成基础模型训练脚本...")
    
    script_content = '''#!/usr/bin/env python3
"""
基础模型训练脚本
先训练UP和DOWN模型，然后再训练元模型
"""

import sys
import os
import time

# 添加项目路径
sys.path.append('.')
sys.path.append('src')

def train_base_models():
    """训练基础模型"""
    print("🚀 开始训练基础模型...")
    
    try:
        # 导入主训练模块
        import main
        
        # 训练UP模型
        print("\\n📈 训练UP模型...")
        print("请在GUI中选择 'BTC_15m_UP' 目标，然后点击 '训练' 按钮")
        print("或者修改 config.py 中的 TARGETS_TO_TRAIN 为 ['BTC_15m_UP']")
        
        # 训练DOWN模型  
        print("\\n📉 训练DOWN模型...")
        print("请在GUI中选择 'BTC_15m_DOWN' 目标，然后点击 '训练' 按钮")
        print("或者修改 config.py 中的 TARGETS_TO_TRAIN 为 ['BTC_15m_DOWN']")
        
        print("\\n✅ 基础模型训练完成后，即可进行元模型训练")
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("请确保在正确的项目目录中运行此脚本")
    except Exception as e:
        print(f"❌ 训练过程出错: {e}")

if __name__ == "__main__":
    train_base_models()
'''
    
    script_path = "train_base_models.py"
    with open(script_path, 'w', encoding='utf-8') as f:
        f.write(script_content)
    
    print(f"  ✓ 已生成训练脚本: {script_path}")
    return script_path

def provide_solutions(missing_files):
    """提供解决方案"""
    print("\n" + "="*60)
    print("🛠️  解决方案建议")
    print("="*60)
    
    if missing_files:
        print(f"❌ 发现缺失的基础模型: {', '.join(missing_files)}")
        print()
        print("📋 解决步骤:")
        print("1. 首先训练基础模型 (UP 和 DOWN)")
        print("2. 确保模型文件正确保存到对应目录")
        print("3. 验证模型文件完整性")
        print("4. 最后训练元模型")
        print()
        
        print("🎯 具体操作方法:")
        print()
        print("方法一：使用GUI界面")
        print("  1. 运行 python main.py 启动GUI")
        print("  2. 在目标选择中选择 'BTC_15m_UP'")
        print("  3. 点击 '训练' 按钮，等待UP模型训练完成")
        print("  4. 在目标选择中选择 'BTC_15m_DOWN'") 
        print("  5. 点击 '训练' 按钮，等待DOWN模型训练完成")
        print("  6. 点击 '训练元模型' 按钮")
        print()
        
        print("方法二：修改配置文件")
        print("  1. 编辑 config.py 文件")
        print("  2. 设置 TARGETS_TO_TRAIN = ['BTC_15m_UP']")
        print("  3. 运行 python main.py，等待UP模型训练完成")
        print("  4. 设置 TARGETS_TO_TRAIN = ['BTC_15m_DOWN']")
        print("  5. 运行 python main.py，等待DOWN模型训练完成")
        print("  6. 使用GUI或代码训练元模型")
        print()
        
        print("⚠️  注意事项:")
        print("  - 基础模型训练可能需要较长时间（几小时到几天）")
        print("  - 确保有足够的历史数据用于训练")
        print("  - 训练过程中避免中断程序")
        print("  - 定期检查训练日志和错误信息")
        
    else:
        print("✅ 所有基础模型文件都存在！")
        print("可以直接进行元模型训练。")
        print()
        print("🎯 元模型训练方法:")
        print("  1. 运行 python main.py 启动GUI")
        print("  2. 点击 '训练元模型' 按钮")
        print("  3. 等待训练完成")

def main():
    """主函数"""
    print("🔧 基础模型缺失问题诊断工具")
    print("="*60)
    
    # 检查目录结构
    check_model_directories()
    
    # 检查预期文件
    missing_files = check_expected_model_files()
    
    # 检查配置文件
    check_config_files()
    
    # 生成训练脚本
    script_path = generate_training_script()
    
    # 提供解决方案
    provide_solutions(missing_files)
    
    print("\n" + "="*60)
    print("📞 如需更多帮助，请查看:")
    print("  - 控制台输出的详细错误信息")
    print("  - config.py 中的配置参数")
    print("  - docs/ 目录中的相关文档")
    print(f"  - 生成的训练脚本: {script_path}")

if __name__ == "__main__":
    main()
