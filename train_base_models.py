#!/usr/bin/env python3
"""
基础模型训练脚本
先训练UP和DOWN模型，然后再训练元模型
"""

import sys
import os
import time

# 添加项目路径
sys.path.append('.')
sys.path.append('src')

def train_base_models():
    """训练基础模型"""
    print("🚀 开始训练基础模型...")
    
    try:
        # 导入主训练模块
        import main
        
        # 训练UP模型
        print("\n📈 训练UP模型...")
        print("请在GUI中选择 'BTC_15m_UP' 目标，然后点击 '训练' 按钮")
        print("或者修改 config.py 中的 TARGETS_TO_TRAIN 为 ['BTC_15m_UP']")
        
        # 训练DOWN模型  
        print("\n📉 训练DOWN模型...")
        print("请在GUI中选择 'BTC_15m_DOWN' 目标，然后点击 '训练' 按钮")
        print("或者修改 config.py 中的 TARGETS_TO_TRAIN 为 ['BTC_15m_DOWN']")
        
        print("\n✅ 基础模型训练完成后，即可进行元模型训练")
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("请确保在正确的项目目录中运行此脚本")
    except Exception as e:
        print(f"❌ 训练过程出错: {e}")

if __name__ == "__main__":
    train_base_models()
